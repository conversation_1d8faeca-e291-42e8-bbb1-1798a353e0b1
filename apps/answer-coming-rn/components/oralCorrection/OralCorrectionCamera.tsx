import ImageResizer from '@bam.tech/react-native-image-resizer';
import {
  YTImage,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import {
  useNavigationBarBarHeight,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { showToast, useDidHide, useDidShow } from '@jgl/utils';
import { useUnmount } from 'ahooks';
import { CameraView } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  Platform,
  StyleSheet,
  View,
  type LayoutChangeEvent,
} from 'react-native';
import RNCanvas from 'react-native-canvas';

interface OralCorrectionCameraProps {
  onTakePhoto: (imageUri: string) => void;
  loading?: boolean;
}

export const OralCorrectionCamera: React.FC<OralCorrectionCameraProps> = ({
  onTakePhoto,
  loading = false,
}) => {
  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const barHeight = useNavigationBarBarHeight();
  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const cameraRef = useRef<CameraView>(null);
  const [cameraActive, setCameraActive] = useState(true);
  const [enableTorch, setEnableTorch] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [canvasSize, setCanvasSize] = useState<{
    width: number;
    height: number;
  }>();

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setCanvasSize({ width, height });
  }, []);

  useDidShow(() => {
    setCameraActive(true);
  });

  useDidHide(() => {
    setCameraActive(false);
  });

  useUnmount(() => {
    setCameraActive(false);
  });

  React.useEffect(() => {
    setIsUploading(loading);
  }, [loading]);

  const onPressNavBack = useCallback(() => {
    router.back();
  }, []);

  const processImage = useCallback(async (uri: string) => {
    try {
      const resizedImage = await ImageResizer.createResizedImage(
        uri,
        1200,
        1600,
        'JPEG',
        80,
        0,
        undefined,
        false,
        {
          mode: 'contain',
          onlyScaleDown: true,
        },
      );
      return resizedImage.uri;
    } catch (error) {
      console.error('图片处理失败:', error);
      return uri;
    }
  }, []);

  const onCommitPhoto = useCallback(
    async (photoInfo: {
      url: string;
      width: number;
      height: number;
      size: number;
    }) => {
      if (isUploading) return;

      try {
        setIsUploading(true);
        const processedUri = await processImage(photoInfo.url);
        onTakePhoto(processedUri);
      } catch (error) {
        console.error('OralCorrectionCamera - onCommitPhoto - error', error);
        setIsUploading(false);
        showToast({
          title: '图片处理失败',
        });
      }
    },
    [isUploading, processImage, onTakePhoto],
  );

  const onPressTakePhoto = useCallback(async () => {
    if (cameraRef.current && !isUploading) {
      try {
        const result = await cameraRef.current.takePictureAsync({
          base64: false,
          quality: 1,
        });

        if (result) {
          onCommitPhoto({
            url: result.uri,
            width: result.width,
            height: result.height,
            size: 0,
          });
        }
      } catch (error) {
        console.error('拍照失败:', error);
        showToast({
          title: '拍照失败，请重试',
        });
      }
    }
  }, [isUploading, onCommitPhoto]);

  const onPressOpenAlbum = useCallback(async () => {
    if (isUploading) return;

    const authResult = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });

    if (authResult) {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 1,
        selectionLimit: 1,
      });

      if (!result.canceled && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset) {
          const { uri, width, height } = asset;
          onCommitPhoto({
            url: uri,
            width,
            height,
            size: 0,
          });
        }
      }
    }
  }, [isUploading, checkAndRequestPermission, onCommitPhoto]);

  const renderNavBar = useMemo(() => {
    return (
      <YTXStack
        position='absolute'
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w='$full'
        h={safeInsets.top + navigationBarHeight}
        ai='center'
        bg='transparent'
      >
        <YTXStack
          position='absolute'
          w='$full'
          left={0}
          right={0}
          top={safeInsets.top}
          h={Platform.OS === 'ios' ? barHeight : undefined}
          py={10}
          px={16}
          jc='space-between'
          ai='center'
          bg='transparent'
        >
          <YTTouchable
            ai='center'
            jc='center'
            onPress={onPressNavBack}
            style={{
              width: 32,
              height: 32,
            }}
          >
            <YTImage
              source={require('../../assets/images/icon_back_white_bg_black_line.png')}
              w={32}
              h={32}
            />
          </YTTouchable>
        </YTXStack>
      </YTXStack>
    );
  }, [barHeight, navigationBarHeight, onPressNavBack, safeInsets.top]);

  const renderBottomBar = useMemo(() => {
    return (
      <YTXStack
        position='fixed'
        w='$full'
        left={0}
        right={0}
        bottom={0}
        px={24}
        pb={safeInsets.bottom}
        h={142 + safeInsets.bottom}
        ai='center'
        justifyContent='space-between'
        bg='transparent'
        zIndex={9999}
      >
        <YTTouchable
          w={44}
          h={44}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressOpenAlbum}
        >
          <YTImage
            source={require('../../assets/images/ic_open_album.png')}
            w={24}
            h={24}
          />
        </YTTouchable>
        <YTTouchable
          w={64}
          h={64}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressTakePhoto}
        >
          <YTImage
            source={require('../../assets/images/ic_take_photo_btn.png')}
            w={64}
            h={64}
          />
        </YTTouchable>
        <YTTouchable
          w={44}
          h={44}
          bg={enableTorch === false ? '#262626' : 'white'}
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={() => setEnableTorch((prev) => !prev)}
        >
          <YTImage
            source={
              enableTorch === false
                ? require('../../assets/images/ic_flash_light_off.png')
                : require('../../assets/images/ic_flash_light_on.png')
            }
            w={24}
            h={24}
          />
        </YTTouchable>
      </YTXStack>
    );
  }, [enableTorch, onPressOpenAlbum, onPressTakePhoto, safeInsets.bottom]);

  const handleCanvas = useCallback(
    (canvas: RNCanvas | null) => {
      if (!canvas || !canvasSize) return;

      const { width, height } = canvasSize;
      canvas.width = width;
      canvas.height = height;

      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, width, height);

      // 绘制参考线
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(40, height / 2);
      ctx.lineTo(width - 40, height / 2);
      ctx.stroke();

      // 绘制提示文字
      ctx.font = '16px sans-serif';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('一次拍摄一整页', width / 2, height / 2 - 40);
      ctx.fillText('题目与参考线平行', width / 2, height / 2 - 20);
    },
    [canvasSize],
  );

  const renderCanvas = useMemo(() => {
    if (canvasSize) {
      return (
        <View pointerEvents='none' style={StyleSheet.absoluteFill}>
          <RNCanvas ref={handleCanvas} />
        </View>
      );
    }
  }, [canvasSize, handleCanvas]);

  return (
    <YTYStack flex={1} bg='black' position='relative'>
      {renderNavBar}
      <YTYStack flex={1} position='relative' bg='transparent'>
        {Platform.OS === 'ios' ? (
          <CameraView
            active={cameraActive}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: 'black',
            }}
            ref={cameraRef}
            facing='back'
            onLayout={!canvasSize ? onLayout : undefined}
            enableTorch={enableTorch}
            onMountError={(e) => {
              console.error('相机初始化失败:', e.message);
            }}
          />
        ) : cameraActive ? (
          <CameraView
            active={cameraActive}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: 'black',
            }}
            ref={cameraRef}
            facing='back'
            enableTorch={enableTorch}
            onLayout={!canvasSize ? onLayout : undefined}
            onMountError={(e) => {
              console.error('相机初始化失败:', e.message);
            }}
          />
        ) : null}
        {renderCanvas}
      </YTYStack>
      {renderBottomBar}
    </YTYStack>
  );
};
