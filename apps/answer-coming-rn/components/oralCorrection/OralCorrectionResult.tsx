import {
  YTImage,
  YTSc<PERSON>View,
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { useSafeAreaInsets, useImageSize } from '@jgl/biz-func';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { OralCorrectionResult as ResultType } from '../../hooks/useOralCorrection';
import {
  OralCorrectionBottomSheet,
  OralCorrectionBottomSheetRef,
} from './OralCorrectionBottomSheet';

interface OralCorrectionResultProps {
  visible: boolean;
  imageUrl?: string;
  checkDTO?: ResultType;
  loading?: boolean;
  onRetake?: () => void;
  onViewHistory?: () => void;
}

interface ResultInfo {
  coord: number[];
  correct: number;
  judgeResult: number;
  value: string;
  answer?: {
    value: string;
    type: string;
  };
}

export const OralCorrectionResult: React.FC<OralCorrectionResultProps> = ({
  visible,
  imageUrl,
  checkDTO,
  loading = false,
  onRetake,
  onViewHistory,
}) => {
  const { bottom } = useSafeAreaInsets();
  const [selectedAnswerItems, setSelectedAnswerItems] = useState<any[]>([]);
  const bottomSheetRef = useRef<OralCorrectionBottomSheetRef>(null);

  // 使用useImageSize hook来处理图片尺寸和缩放
  // 注意：与小程序版本一致，使用originImageUrl计算尺寸，但显示imageUrl
  const { size, init } = useImageSize({ imageUrl: checkDTO?.originImageUrl });

  useEffect(() => {
    init();
  }, [init]);

  const rightPercent = useMemo(() => {
    if (!checkDTO?.rightNums || !checkDTO?.errorNums) return 0;
    const total = checkDTO.rightNums + checkDTO.errorNums;
    return total > 0 ? (checkDTO.rightNums / total) * 100 : 0;
  }, [checkDTO?.rightNums, checkDTO?.errorNums]);

  const renderAllMarks = useCallback(() => {
    if (!checkDTO?.resultInfos || !size.width || !size.height) {
      return null;
    }

    return checkDTO.resultInfos.map((result: any, index: number) => {
      const recResults = result.recResult || [];

      return recResults.map((recResult: ResultInfo, recIndex: number) => {
        const [x1 = 0, y1 = 0, x2 = 0, y2 = 0] = recResult.coord;

        // 使用useImageSize计算的尺寸，与小程序版本保持一致
        const left = size.width * x1;
        const top = size.height * y1;
        const width = size.width * (x2 - x1);
        const height = size.height * (y2 - y1);

        // 判断题目状态
        const isRight = recResult.judgeResult === 1; // 正确
        const isError = recResult.judgeResult === 0; // 错误

        if (!isRight && !isError) return null; // 只显示正确和错误的

        const handlePress = () => {
          if (isError && recResult.answer) {
            // 点击错误题目，显示底部弹窗
            setSelectedAnswerItems([
              {
                recValue: recResult.value,
                value: recResult.answer.value,
                type: recResult.answer.type,
              },
            ]);
            bottomSheetRef.current?.present();
          }
        };

        return (
          <YTTouchable
            key={`${index}-${recIndex}`}
            position='absolute'
            style={{
              left,
              top,
              width,
              height,
            }}
            onPress={handlePress}
          >
            <YTView
              w='100%'
              h='100%'
              borderWidth={isRight ? 2 : 2}
              borderColor={isRight ? '#52C41A' : 'red'}
              borderRadius={4}
              bg={isRight ? 'rgba(82,196,26,0.1)' : 'rgba(255,0,0,0.1)'}
              ai='center'
              jc='center'
            >
              <YTView
                w={20}
                h={20}
                borderRadius={10}
                bg={isRight ? '#52C41A' : 'red'}
                ai='center'
                jc='center'
              >
                <YTText color='white' fontSize={12} fontWeight='bold'>
                  {isRight ? '✓' : '✕'}
                </YTText>
              </YTView>
            </YTView>
          </YTTouchable>
        );
      });
    });
  }, [checkDTO?.resultInfos, size]);

  const renderResultDescription = useMemo(() => {
    if (!checkDTO?.rightNums && !checkDTO?.errorNums) return null;

    const total = (checkDTO.rightNums || 0) + (checkDTO.errorNums || 0);

    return (
      <YTXStack ai='center' gap={4}>
        <YTText fontSize={18} color='#1f1f1f' fontWeight='bold'>
          共
        </YTText>
        <YTText fontSize={18} color='#52C41A' fontWeight='bold'>
          {total}
        </YTText>
        <YTText fontSize={18} color='#1f1f1f' fontWeight='bold'>
          题，正确率
        </YTText>
        <YTText fontSize={18} color='#52C41A' fontWeight='bold'>
          {rightPercent.toFixed(1)}%
        </YTText>
      </YTXStack>
    );
  }, [checkDTO?.rightNums, checkDTO?.errorNums, rightPercent]);

  const renderContent = () => {
    if (loading) {
      return (
        <YTYStack flex={1} ai='center' jc='center'>
          <YTText fontSize={16} color='#666'>
            正在批改中...
          </YTText>
        </YTYStack>
      );
    }

    if (checkDTO?.errorMsg) {
      return (
        <YTYStack flex={1} ai='center' jc='center' px={20}>
          <YTText fontSize={16} color='#ff4d4f' textAlign='center'>
            {checkDTO.errorMsg}
          </YTText>
          {onRetake && (
            <YTTouchable
              mt={20}
              px={20}
              py={10}
              bg='#1890ff'
              borderRadius={8}
              onPress={onRetake}
            >
              <YTText color='white' fontSize={16}>
                重新拍照
              </YTText>
            </YTTouchable>
          )}
        </YTYStack>
      );
    }

    if (!checkDTO?.resultInfos || checkDTO.resultInfos.length === 0) {
      return (
        <YTYStack flex={1} ai='center' jc='center' px={20}>
          <YTText fontSize={16} color='#666' textAlign='center'>
            暂未识别出题目，再拍一次吧~
          </YTText>
          {onRetake && (
            <YTTouchable
              mt={20}
              px={20}
              py={10}
              bg='#1890ff'
              borderRadius={8}
              onPress={onRetake}
            >
              <YTText color='white' fontSize={16}>
                重新拍照
              </YTText>
            </YTTouchable>
          )}
        </YTYStack>
      );
    }

    return (
      <YTScrollView flex={1} showsVerticalScrollIndicator={false}>
        <YTYStack flex={1}>
          {/* 图片和标记区域 */}
          <YTView position='relative' ai='center' py={20}>
            {checkDTO?.imageUrl && size.width > 0 && size.height > 0 && (
              <YTImage
                source={{ uri: checkDTO.imageUrl }}
                style={{
                  width: size.width,
                  height: size.height,
                }}
                contentFit='contain'
              />
            )}
            {renderAllMarks()}
          </YTView>

          {/* 结果描述 */}
          <YTYStack ai='center' py={20}>
            {renderResultDescription}
            <YTXStack ai='center' mt={10} gap={4}>
              <YTText color='#4E5969' fontSize={14}>
                点击
              </YTText>
              <YTView
                w={20}
                h={20}
                borderRadius={10}
                bg='red'
                ai='center'
                jc='center'
              >
                <YTText color='white' fontSize={10}>
                  ✕
                </YTText>
              </YTView>
              <YTText color='#4E5969' fontSize={14}>
                标记的错误答案题，可查看正确答案
              </YTText>
            </YTXStack>
          </YTYStack>
        </YTYStack>
      </YTScrollView>
    );
  };

  if (!visible) return null;

  return (
    <YTYStack flex={1} bg='white' style={{ paddingBottom: bottom || 16 }}>
      {renderContent()}

      {/* 底部按钮 */}
      <YTXStack px={16} py={12} gap={12}>
        {onViewHistory && (
          <YTTouchable
            flex={1}
            py={12}
            bg='#f5f5f5'
            borderRadius={8}
            ai='center'
            onPress={onViewHistory}
          >
            <YTText color='#666' fontSize={16}>
              查看记录
            </YTText>
          </YTTouchable>
        )}
        {onRetake && (
          <YTTouchable
            flex={1}
            py={12}
            bg='#1890ff'
            borderRadius={8}
            ai='center'
            onPress={onRetake}
          >
            <YTText color='white' fontSize={16}>
              再拍一次
            </YTText>
          </YTTouchable>
        )}
      </YTXStack>

      {/* 底部弹窗 */}
      <OralCorrectionBottomSheet
        ref={bottomSheetRef}
        answerItems={selectedAnswerItems}
      />
    </YTYStack>
  );
};
