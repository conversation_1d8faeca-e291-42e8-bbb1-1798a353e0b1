import {
  YTS<PERSON><PERSON>View,
  YTText,
  YTTouchable,
  YTView,
  YTYStack,
} from '@bookln/cross-platform-components';
import { X } from '@bookln/icon-lucide';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import React, { forwardRef, useCallback, useMemo } from 'react';
import { MarkdownResult } from '../MarkdownResult';

interface AnswerItem {
  recValue: string;
  value: string;
  type: string;
}

interface OralCorrectionBottomSheetProps {
  answerItems: AnswerItem[];
}

export interface OralCorrectionBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

export const OralCorrectionBottomSheet = forwardRef<
  OralCorrectionBottomSheetRef,
  OralCorrectionBottomSheetProps
>(({ answerItems }, ref) => {
  const bottomSheetRef = React.useRef<BottomSheetModal>(null);

  const snapPoints = useMemo(() => ['50%'], []);

  const present = useCallback(() => {
    bottomSheetRef.current?.present();
  }, []);

  const dismiss = useCallback(() => {
    bottomSheetRef.current?.dismiss();
  }, []);

  React.useImperativeHandle(ref, () => ({
    present,
    dismiss,
  }));

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.6}
      />
    ),
    [],
  );

  const renderHandle = useCallback(() => {
    return (
      <YTView
        w="100%"
        h={50}
        ai="center"
        jc="center"
        borderBottomWidth={1}
        borderBottomColor="#f0f0f0"
        position="relative"
      >
        <YTView
          w={40}
          h={4}
          bg="#d9d9d9"
          borderRadius={2}
          position="absolute"
          top={8}
        />
        <YTTouchable
          position="absolute"
          right={16}
          top={11}
          w={28}
          h={28}
          ai="center"
          jc="center"
          onPress={dismiss}
        >
          <X size={20} color="#666" />
        </YTTouchable>
      </YTView>
    );
  }, [dismiss]);

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      backdropComponent={renderBackdrop}
      handleComponent={renderHandle}
      enableDismissOnClose
      enablePanDownToClose
    >
      <BottomSheetScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingBottom: 40,
        }}
      >
        <YTYStack gap={20}>
          {/* 识别结果 */}
          <YTYStack gap={8}>
            <YTText fontSize={16} fontWeight="600" color="#1f1f1f">
              识别结果
            </YTText>
            <YTView flexDirection="row" flexWrap="wrap" gap={8}>
              {answerItems.map((item, index) => (
                <YTView
                  key={`rec-${index}`}
                  bg="#f5f5f5"
                  borderRadius={8}
                  px={12}
                  py={8}
                >
                  <MarkdownResult
                    content={`$${item.recValue}$`}
                    isSupportLatex
                  />
                </YTView>
              ))}
            </YTView>
          </YTYStack>

          {/* 正确答案 */}
          <YTYStack gap={8}>
            <YTText fontSize={16} fontWeight="600" color="#1f1f1f">
              正确答案
            </YTText>
            <YTView flexDirection="row" flexWrap="wrap" gap={8}>
              {answerItems.map((item, index) => (
                <YTView
                  key={`correct-${index}`}
                  bg="#f0f9ff"
                  borderRadius={8}
                  px={12}
                  py={8}
                  borderWidth={1}
                  borderColor="#1890ff"
                >
                  <MarkdownResult
                    content={`$${item.value}$`}
                    isSupportLatex
                  />
                </YTView>
              ))}
            </YTView>
          </YTYStack>
        </YTYStack>
      </BottomSheetScrollView>
    </BottomSheetModal>
  );
});

OralCorrectionBottomSheet.displayName = 'OralCorrectionBottomSheet';
