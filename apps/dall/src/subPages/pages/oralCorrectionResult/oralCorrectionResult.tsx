import { Popup } from '@antmjs/vantui';
import { AiResultState, StateView } from '@jgl/biz-components';
import { MarkdownResult } from '@jgl/biz-components-rojer-katex-mini';
import {
  CoordTypeEnum,
  fontSizeStyle,
  useImageSize,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { Image, NavigationBar, WithLogin } from '@jgl/components';
import { container } from '@jgl/container';
import Icon from '@jgl/icon';
import { AcIcon } from '@jgl/icon/src';
import { JGLText } from '@jgl/ui';
import { useRouterParams } from '@jgl/utils';
import { ScrollView, Text, View } from '@tarojs/components';
import { redirectTo, navigateBack } from '@tarojs/taro';
import { useInterval, useMount } from 'ahooks';
import routerMap from 'config/routerMap';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { queryUserCorrectDetailByRecordId } from 'src/api-dto/api';
import { subPagesKey } from 'src/constants';
import { handleJumpSubPages } from 'src/utils';

type AnswerItem = {
  /** 识别结果 */
  recValue: string;
  /** 正确答案 */
  value: string;
};

/**
 * 口算检查结果
 */
const OralCorrectionResultScreen = () => {
  const { recordId } = useRouterParams();
  const { bottom } = useSafeAreaInsets();
  const [checkDTO, setCheckDTO] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [answerItems, setAnswerItems] = useState<AnswerItem[]>([]); // 识别列表和答案列表
  const [isOpened, setIsOpened] = useState<boolean>(false); // 是否打开弹框查看正确答案
  const { size, init } = useImageSize({ imageUrl: checkDTO?.originImageUrl });

  const [intervalTime, setIntervalTime] = useState<number | undefined>();

  const { imageUrl } = checkDTO ?? {};

  const getResult = useCallback(async () => {
    if (!recordId) {
      return;
    }
    const { data } = await container.net().fetch(
      queryUserCorrectDetailByRecordId({
        recordId: String(recordId),
      }),
    );

    // 未批改成功清除定时器
    if (data?.errorCode) {
      setIntervalTime(undefined);
      setLoading(false);
      setCheckDTO(data);

      // 批改成功清除定时器
    } else if (data?.resultInfos?.length) {
      setIntervalTime(undefined);
      setLoading(false);
      setCheckDTO(data);
    }
  }, [recordId]);

  useInterval(() => {
    getResult();
  }, intervalTime);

  useMount(() => {
    setLoading(true);
    setIntervalTime(1000);
  });

  useEffect(() => {
    init();
  }, [init]);

  /**
   * 渲染题目作答状态，正确，错误，未作答
   */
  const renderExamStatus = useCallback((answer) => {
    /**
     * 作答正确，并且题干存在
     */
    const isRight = [CoordTypeEnum.Right, CoordTypeEnum.HalfRight].includes(
      answer.judgeResult,
    );

    /**
     * 作答错误。 value会给出正确答案，value的第一个元素对象是有数据的
     */
    const isError = [CoordTypeEnum.Wrong].includes(answer.judgeResult);

    /**
     * 未作答，即题目为空
     */
    const notAnswer = [
      CoordTypeEnum.NotContent,
      CoordTypeEnum.QuestionMark,
    ].includes(answer.judgeResult);

    let imageSrc;
    if (isRight) {
      imageSrc = Icon.rightGou;
    } else if (isError) {
      imageSrc = Icon.errorCircle;
    } else if (notAnswer) {
      imageSrc = Icon.question;
    }
    return imageSrc ? (
      <Image
        onClick={() => {
          if (isError) {
            setAnswerItems(answer?.answer || []);
            setIsOpened(true);
          }
        }}
        className='aspect-square max-h-[28PX] max-w-[28PX]'
        src={imageSrc}
      />
    ) : null;
  }, []);
  /**
   * 渲染作答框
   */
  const renderRect = useCallback(
    (list?: Record<string, any>[]) => {
      return list?.map((answer) => {
        /**
         * 左上角与右下角的坐标，格式为小数，即与原图宽高的比例
         */
        const [x1 = 0, y1 = 0, x2 = 0, y2 = 0] = answer?.coord || [];

        return (
          <View
            className='flex-center absolute rounded-[8PX] '
            style={{
              width: `${size.width * (x2 - x1)}PX`,
              height: `${size.height * (y2 - y1)}PX`,
              left: `${size.width * x1}PX`,
              top: `${size.height * y1}PX`,
              border: '1PX solid white',
            }}
            key={answer?.coord.join(',')}
          >
            {renderExamStatus(answer)}
          </View>
        );
      });
    },
    [renderExamStatus, size.height, size.width],
  );

  /**
   * 渲染压缩后的图片
   */
  const renderImage = useMemo(
    () =>
      imageUrl ? (
        <Image
          src={imageUrl}
          mode='widthFix'
          style={{ width: `${size.width}PX`, height: `${size.height}PX` }}
        />
      ) : null,
    [imageUrl, size],
  );

  const rightPercent = useMemo(() => {
    const total =
      (checkDTO?.rightNums ?? 0) +
      (checkDTO?.errorNums ?? 0) +
      (checkDTO?.noReplyNums ?? 0);
    return ((checkDTO?.rightNums ?? 0) / total) * 100;
  }, [checkDTO?.rightNums, checkDTO?.errorNums, checkDTO?.noReplyNums]);

  /**
   * 渲染批改结果描述
   */
  const renderResultDescription = useMemo(
    () =>
      checkDTO?.resultInfos?.length ? (
        <>
          <View className='flex-center mb-[8PX] w-full'>
            <Text
              className='font-semibold leading-[24PX] text-[#1f1f1f]'
              style={fontSizeStyle[18]}
            >
              正确
            </Text>
            <Text
              className='mx-[4PX] font-semibold leading-[24PX] text-[#52C41A] '
              style={fontSizeStyle[18]}
            >
              {checkDTO?.rightNums}
            </Text>
            <Text
              className='font-semibold leading-[24PX] text-[#1f1f1f]'
              style={fontSizeStyle[18]}
            >
              题，
            </Text>
            <Text
              className='font-semibold text-[#1f1f1f]'
              style={fontSizeStyle[16]}
            >
              错误
            </Text>
            <Text
              className='mx-[4PX] font-semibold leading-[24PX] text-[#FF4D4F] '
              style={fontSizeStyle[18]}
            >
              {checkDTO?.errorNums}
            </Text>
            <Text
              className='font-semibold leading-[24PX] text-[#1f1f1f]'
              style={fontSizeStyle[18]}
            >
              题，
            </Text>
            <Text
              className='font-semibold leading-[24PX] text-[#1f1f1f] '
              style={fontSizeStyle[18]}
            >
              正确率
            </Text>
            <Text
              className='ml-[4PX] font-semibold leading-[24PX] text-[#52C41A] '
              style={fontSizeStyle[18]}
            >
              {rightPercent.toFixed(2)}%
            </Text>
          </View>
        </>
      ) : null,
    [
      checkDTO?.errorNums,
      checkDTO?.resultInfos?.length,
      checkDTO?.rightNums,
      rightPercent,
    ],
  );

  const handleToRecord = useCallback(() => {
    handleJumpSubPages(routerMap.oralCorrectionRecord);
  }, []);

  const onClickTakeAgain = useCallback(() => {
    navigateBack({
      fail: () =>
        redirectTo({
          url: `/${subPagesKey}/${routerMap.oralCorrection}`,
        }),
    });
  }, []);

  return (
    <View
      className=' flex h-full w-full flex-col overflow-hidden bg-white'
      style={{ paddingBottom: bottom || 16 }}
    >
      <NavigationBar title='批改结果' back type='left' />

      <StateView
        ytClassName='flex-1 w-full flex-col justify-center overflow-hidden'
        isLoading={loading}
        isEmpty={checkDTO?.resultInfos?.length === 0}
        error={checkDTO?.errorMsg ? new Error(checkDTO?.errorMsg) : null}
        emptyProps={{
          render: () => <AiResultState empty={'暂未识别出题目，再拍一次吧~'} />,
        }}
        errorProps={{
          render: () => <AiResultState error={checkDTO?.errorMsg} />,
        }}
      >
        <View className='flex flex-1 overflow-hidden'>
          <ScrollView
            enableFlex
            showScrollbar={false}
            scrollY
            className='relative flex w-full flex-1 flex-col'
          >
            <View className='flex w-full flex-1 flex-col'>
              <View className='relative flex w-full flex-1'>
                {renderImage}

                {checkDTO?.resultInfos?.map((result) => {
                  return renderRect(result?.recResult);
                })}
              </View>

              <View className='flex-center h-[86PX] w-full flex-shrink-0 flex-col'>
                {renderResultDescription}
                <View className='flex items-center'>
                  <JGLText className='text-[#4E5969]'>点击</JGLText>
                  <Image
                    className='h-[22PX] w-[32PX]'
                    src={AcIcon.IcWrongCircle}
                  />
                  <JGLText className='text-[#4E5969]'>
                    标记的错误答案题，可查看正确答案
                  </JGLText>
                </View>
              </View>
            </View>

            <View className='sticky bottom-0 mt-[12PX] flex h-[60PX] w-full flex-shrink-0 items-center space-x-[12PX] bg-white px-[16PX]'>
              <View className='flex-center flex-col' onClick={handleToRecord}>
                <Image
                  src={AcIcon.IcCorrectRecord}
                  className='h-[24PX] w-[24PX]'
                />
                <JGLText size='2xs' className='text-[#60646C]'>
                  批改记录
                </JGLText>
              </View>

              <View
                className='flex-center h-[44PX] flex-1 rounded-[32PX] bg-[#165DFF]'
                onClick={onClickTakeAgain}
              >
                <Image
                  className='mr-[2PX] h-[21PX] w-[20PX]'
                  src={AcIcon.IcCorrectTake}
                />
                <Text
                  className='font-medium text-white'
                  style={{ fontSize: 16, lineHeight: '22PX' }}
                >
                  再拍一张
                </Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </StateView>

      <Popup
        show={isOpened}
        position='bottom'
        safeAreaInsetBottom
        onClose={() => setIsOpened(false)}
      >
        <View
          className='flex max-h-[300PX] w-full flex-col gap-[20PX] overflow-auto bg-white p-[20PX]'
          style={{
            fontSize: '14PX',
          }}
        >
          <View className='flex flex-col gap-[8PX]'>
            <View className='font-semibold'>识别结果</View>
            <View className='flex flex-wrap gap-[8PX]'>
              {answerItems.map((item: AnswerItem, index: number) => {
                return (
                  <MarkdownResult
                    key={index}
                    content={`$${item.recValue}$`}
                    isSupportLatex
                    noNeedAiEnd
                  />
                );
              })}
            </View>
          </View>
          <View className='flex flex-col gap-[8PX]'>
            <View className='font-semibold'>正确答案</View>
            <View className='flex flex-wrap gap-[8PX]'>
              {answerItems.map((item: AnswerItem, index: number) => {
                return (
                  <MarkdownResult
                    key={index}
                    content={`$${item.value}$`}
                    isSupportLatex
                    noNeedAiEnd
                  />
                );
              })}
            </View>
          </View>
        </View>
      </Popup>
    </View>
  );
};

export default WithLogin(OralCorrectionResultScreen);
